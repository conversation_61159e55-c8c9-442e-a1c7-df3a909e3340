import cv2 as cv
import numpy as np


def max_pool_resize(mask, scale_factor):
    """
    Resize mask using maximum pooling to preserve important features.

    Args:
        mask: Input mask array
        scale_factor: Scale factor (e.g., 0.125 for 1/8 size)

    Returns:
        Resized mask using maximum pooling
    """
    if scale_factor >= 1.0:
        return cv.resize(
            mask, (0, 0), fx=scale_factor, fy=scale_factor, interpolation=cv.INTER_NEAREST
        )

    # Calculate pooling kernel size
    kernel_size = int(1 / scale_factor)

    # Calculate output dimensions
    h, w = mask.shape[:2]
    new_h = h // kernel_size
    new_w = w // kernel_size

    # Crop to make dimensions divisible by kernel_size
    cropped_mask = mask[: new_h * kernel_size, : new_w * kernel_size]

    # Reshape for max pooling
    reshaped = cropped_mask.reshape(new_h, kernel_size, new_w, kernel_size)

    # Apply max pooling
    pooled = np.max(reshaped, axis=(1, 3))

    return pooled

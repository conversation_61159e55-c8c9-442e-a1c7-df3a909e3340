import os
import time
from functools import partial

import cv2 as cv
import matplotlib.pyplot as plt
import numpy as np
from skimage import feature, future, segmentation
from sklearn.ensemble import RandomForestClassifier

from src.utils.labelme_to_mask import parse_labelme_to_mask

# Labelme labels
# Polygons only
# Labels: clean, text
DATA_DIR = 'data/OCR/20250415/images/images_cropped'

# Load all json files
json_files = [f for f in os.listdir(DATA_DIR) if f.endswith('_prep.json')]
image_files = [f.replace('.json', '.png') for f in json_files]

images = [cv.imread(os.path.join(DATA_DIR, f), cv.IMREAD_GRAYSCALE) for f in image_files]
masks = [parse_labelme_to_mask(os.path.join(DATA_DIR, f)) for f in json_files]

# # Crop
# y = 242
# h = 2000
# img = img[y : y + h, :]
# training_labels = training_labels[y : y + h, :]

sigma_min = 1
sigma_max = 16
features_func = partial(
    feature.multiscale_basic_features,
    intensity=True,
    edges=False,
    texture=True,
    sigma_min=sigma_min,
    sigma_max=sigma_max,
    channel_axis=None,  # Use None for grayscale images
)
features = np.concatenate([features_func(img) for img in images], axis=0)
training_labels = np.concatenate(masks, axis=0)
clf = RandomForestClassifier(n_estimators=10, n_jobs=-1, max_depth=10, max_samples=0.05)
clf = future.fit_segmenter(training_labels, features, clf)

full_img = cv.imread(
    'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.png',
    cv.IMREAD_GRAYSCALE,
)
# downscale to 12.5%
full_img = cv.resize(full_img, (0, 0), fx=0.125, fy=0.125)
start = time.perf_counter()
result = future.predict_segmenter(features_func(full_img), clf)
end = time.perf_counter()
print(f'Prediction time: {end - start:.2f} seconds')

fig, ax = plt.subplots(1, 2, sharex=True, sharey=True, figsize=(9, 4))
ax[0].imshow(segmentation.mark_boundaries(full_img, result, mode='thick'))
ax[0].contour(training_labels)
ax[0].set_title('Image, mask and segmentation boundaries')
ax[1].imshow(result)
ax[1].set_title('Segmentation')
fig.tight_layout()
plt.show()

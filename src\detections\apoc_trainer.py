import apoc
import cv2 as cv
import pyclesperanto_prototype as cle
from skimage.io import imread, imshow

from src.utils.labelme_to_mask import parse_labelme_to_mask

# Load image
y = 242
h = 2000
img = cv.imread(
    'data/labeled/20250415_102820052292_prep_crop.png',
    cv.IMREAD_GRAYSCALE,
)

# Load labelme labels
# Polygons only
# labels: clear, text
training_labels = parse_labelme_to_mask(
    'data/labeled/20250415_102820052292_prep_crop.json',
)

# Crop
img = img[y : y + h, :]
training_labels = training_labels[y : y + h, :]

# define features: original image, a blurred version and an edge image
features = apoc.PredefinedFeatureSet.medium_quick.value

# Training
clf = apoc.ObjectSegmenter(opencl_filename='object_segmenter.cl', positive_class_identifier=2)
clf.train(features, training_labels, img)

# Prediction
segmentation_result = clf.predict(image=img)
cle.imshow(segmentation_result, labels=True)
